#!/usr/bin/env python3
"""
Test script for ParamDiscovery tool
This script creates a simple HTTP server to test parameter discovery functionality
"""

import http.server
import socketserver
import threading
import time
import urllib.parse
import json
from typing import Dict, Any

class TestHTTPHandler(http.server.BaseHTTPRequestHandler):
    """Custom HTTP handler for testing parameter discovery"""
    
    def log_message(self, format, *args):
        """Suppress default logging"""
        pass
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_path.query)
        
        # Test scenarios for different parameters
        response_data = self._generate_response(query_params, {})
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.send_header('Content-Length', str(len(response_data)))
        self.end_headers()
        self.wfile.write(response_data.encode())
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        # Parse POST data
        post_params = {}
        content_type = self.headers.get('Content-Type', '')
        
        if 'application/json' in content_type:
            try:
                post_params = json.loads(post_data)
            except json.JSONDecodeError:
                post_params = {}
        else:
            post_params = urllib.parse.parse_qs(post_data)
        
        # Test scenarios for different parameters
        response_data = self._generate_response({}, post_params)
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.send_header('Content-Length', str(len(response_data)))
        self.end_headers()
        self.wfile.write(response_data.encode())
    
    def _generate_response(self, get_params: Dict, post_params: Dict) -> str:
        """Generate response based on parameters"""
        all_params = {**get_params, **post_params}
        
        # Base response
        response = """
        <html>
        <head><title>Test Application</title></head>
        <body>
        <h1>Parameter Discovery Test Page</h1>
        <p>This is a test page for parameter discovery.</p>
        """
        
        # Special parameter behaviors for testing
        special_params = {
            'debug': 'Debug mode enabled! Secret information: FLAG{debug_param_found}',
            'admin': 'Administrative access detected. Admin panel: /admin/dashboard',
            'test': 'Test parameter detected. Running in test mode.',
            'dev': 'Development mode active. Dev tools available.',
            'hidden': 'Hidden parameter found! This should trigger reflection detection.',
            'secret': 'Secret parameter discovered! Internal data exposed.',
            'api_key': 'API key parameter detected. Authentication bypass possible.',
            'token': 'Token parameter found. Session manipulation detected.',
            'user': 'User parameter identified. User enumeration possible.',
            'id': 'ID parameter found. Direct object reference detected.'
        }
        
        # Check for special parameters and add responses
        for param_name, param_values in all_params.items():
            if isinstance(param_values, list):
                param_value = param_values[0] if param_values else ''
            else:
                param_value = str(param_values)
            
            if param_name in special_params:
                response += f"<div class='alert'>{special_params[param_name]}</div>\n"
                # Add reflection for certain parameters
                if param_name in ['debug', 'test', 'hidden']:
                    response += f"<p>Parameter value: {param_value}</p>\n"
            
            # Generic parameter reflection for fuzzval
            if param_value == 'fuzzval':
                response += f"<p>Detected parameter '{param_name}' with value '{param_value}'</p>\n"
        
        # Add some dynamic content to vary response length
        if 'search' in all_params:
            response += "<div>Search results would appear here...</div>\n" * 10
        
        if 'data' in all_params:
            response += "<div>Data processing results...</div>\n" * 5
        
        response += """
        <footer>
        <p>Test server for parameter discovery - Version 1.0</p>
        </footer>
        </body>
        </html>
        """
        
        return response


def start_test_server(port: int = 8080) -> threading.Thread:
    """Start test HTTP server in a separate thread"""
    def run_server():
        with socketserver.TCPServer(("", port), TestHTTPHandler) as httpd:
            print(f"Test server running on http://localhost:{port}")
            httpd.serve_forever()
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    time.sleep(1)  # Give server time to start
    return server_thread


if __name__ == "__main__":
    print("Starting test server for ParamDiscovery tool...")
    server_thread = start_test_server(8080)
    
    print("\nTest server is running on http://localhost:8080")
    print("\nYou can now test the param_scanner.py tool with:")
    print("python param_scanner.py -u http://localhost:8080 -w common_params.txt")
    print("\nOr test POST requests with:")
    print("python param_scanner.py -u http://localhost:8080 -m POST -w common_params.txt")
    print("\nPress Ctrl+C to stop the server...")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down test server...")
