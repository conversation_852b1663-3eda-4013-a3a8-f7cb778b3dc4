# Common HTTP Parameters for Testing
# Web Application Parameters
id
user
username
password
email
token
key
api_key
apikey
access_token
session
sessionid
csrf_token
_token
auth
authorization
bearer
admin
debug
test
dev
development
prod
production
config
settings
option
options
param
parameter
value
data
input
output
search
query
q
filter
sort
order
limit
offset
page
size
count
max
min
start
end
from
to
date
time
timestamp
format
type
mode
action
method
callback
jsonp
redirect
url
link
path
file
filename
upload
download
export
import
view
edit
delete
create
update
save
submit
send
get
post
put
patch
head
trace
connect
options
status
state
enabled
disabled
active
inactive
public
private
hidden
visible
name
title
description
content
body
message
text
html
xml
json
csv
pdf
image
video
audio
category
tag
tags
label
labels
group
groups
role
roles
permission
permissions
scope
scopes
level
priority
weight
score
rating
rank
position
index
number
num
amount
price
cost
total
sum
average
avg
maximum
minimum
first
last
next
previous
prev
current
new
old
recent
latest
popular
trending
featured
recommended
related
similar
random
all
none
any
other
misc
miscellaneous
extra
additional
custom
special
advanced
basic
simple
complex
full
partial
complete
incomplete
success
error
warning
info
notice
alert
confirm
cancel
ok
yes
no
true
false
on
off
enable
disable
show
hide
open
close
expand
collapse
toggle
switch
change
modify
replace
remove
add
insert
append
prepend
before
after
above
below
left
right
top
bottom
center
middle
inside
outside
inner
outer
parent
child
sibling
ancestor
descendant
root
leaf
branch
node
tree
list
array
object
string
integer
int
float
double
boolean
bool
null
undefined
empty
blank
space
tab
newline
return
escape
encode
decode
hash
encrypt
decrypt
compress
decompress
validate
verify
check
test
try
attempt
retry
repeat
loop
iterate
process
execute
run
start
stop
pause
resume
restart
reset
clear
clean
flush
refresh
reload
update
sync
backup
restore
copy
move
rename
duplicate
clone
fork
merge
split
join
combine
separate
divide
multiply
subtract
add
calculate
compute
evaluate
analyze
parse
format
convert
transform
translate
localize
internationalize
globalize
regionalize
customize
personalize
optimize
minimize
maximize
normalize
standardize
regularize
sanitize
filter
sort
group
aggregate
summarize
report
log
track
monitor
watch
observe
listen
notify
alert
warn
inform
message
communicate
broadcast
publish
subscribe
unsubscribe
follow
unfollow
like
unlike
favorite
unfavorite
bookmark
unbookmark
share
unshare
comment
uncomment
rate
unrate
vote
unvote
approve
disapprove
accept
reject
allow
deny
grant
revoke
assign
unassign
attach
detach
connect
disconnect
link
unlink
bind
unbind
map
unmap
associate
disassociate
relate
unrelate
reference
dereference
include
exclude
contain
extract
inject
embed
wrap
unwrap
pack
unpack
bundle
unbundle
compress
decompress
zip
unzip
tar
untar
archive
unarchive
