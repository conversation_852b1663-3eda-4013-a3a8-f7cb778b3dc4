# ParamDiscovery - Usage Notes

## Updated Features

### Changes Made:
1. **Removed Time Anomaly Detection** - No longer uses response time for parameter detection
2. **Concise Output** - Only shows detected parameters, no verbose information
3. **Simplified Format** - Shows parameter name and detection method only
4. **Fixed POST Content-Type** - Now properly sets Content-Type header for POST requests
5. **Enhanced False Positive Filtering** - Multiple layers of validation to eliminate false positives:
   - Filters out error responses and truncated content
   - Very strict similarity thresholds (only flags if <30% similar with substantial content changes)
   - Requires multiple detection methods for medium confidence
   - Intelligent word-level and line-level difference analysis

### Current Detection Methods:
- **REFLECTED** - Test value found in response body (High Confidence)
- **LEN_DIFF** - Content length difference from baseline (High Confidence)  
- **SIMILARITY** - Response similarity below threshold (Medium Confidence)
- **STATUS_CHANGE** - Different HTTP status code (Medium Confidence)
- **HEADER_CHANGE** - Response headers changed (Low Confidence)

## Example Output:

### Real Parameter Detection (POST):
```
ParamDiscovery - Scanning http://testphp.vulnweb.com/guestbook.php
Testing 14 parameters...

[+] name | REFLECTED
[+] text | REFLECTED

Found 2 parameters:
High: 2 | Medium: 0 | Low: 0
Scan completed in 2.5s
```

### Test Site (GET):
```
ParamDiscovery - Scanning https://httpbin.org/get
Testing 11 parameters...

[+] admin | REFLECTED + LEN_DIFF(+40) + SIMILARITY(0.87)
[+] debug | REFLECTED + LEN_DIFF(+40) + SIMILARITY(0.87)
[+] user | REFLECTED + LEN_DIFF(+38) + SIMILARITY(0.65)

Found 3 parameters:
High: 3 | Medium: 0 | Low: 0
Scan completed in 2.4s
```

## Command Usage:

### Basic Usage:
```bash
python3 param_scanner.py -u http://testphp.vulnweb.com/guestbook.php -w burp-params-sample.txt --threads 2
```

### With SecLists (when available):
```bash
python3 param_scanner.py -u http://testphp.vulnweb.com/guestbook.php -w /Users/<USER>/Desktop/SecLists-master/Discovery/Web-Content/burp-parameter-names.txt --threads 2
```

### POST Method:
```bash
python3 param_scanner.py -u http://testphp.vulnweb.com/guestbook.php -m POST -w burp-params-sample.txt --threads 2
```

## Available Wordlists:
- `test_params.txt` - Small test wordlist (11 parameters)
- `burp-params-sample.txt` - Medium wordlist (135 parameters)
- `common_params.txt` - Large wordlist (300+ parameters)

## Notes:
- Tool now silently handles errors and connection issues
- Only detected parameters are displayed
- No SSL warnings or verbose status messages
- Clean, focused output for security testing workflows
- **Significantly enhanced false positive filtering** - eliminates noise from:
  - Error responses and truncated content
  - Dynamic content changes (timestamps, session IDs)
  - Minor response variations that don't indicate real parameters
- Successfully detects real parameters like `text` and `name` in guestbook forms
- Precision-focused: Only shows parameters with high confidence of being real
