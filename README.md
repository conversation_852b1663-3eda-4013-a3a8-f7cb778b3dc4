# ParamDiscovery - HTTP Parameter Discovery Tool

A comprehensive Python HTTP parameter discovery tool that identifies hidden or undocumented parameters in web applications through systematic fuzzing of GET and POST requests.

## Features

- **Multi-layer Detection System**: Uses reflection detection, content-length analysis, response similarity, status code changes, and timing analysis
- **GET and POST Support**: Handles both URL parameters and POST data (form-encoded and JSON)
- **Multi-threading**: Configurable concurrent request processing
- **Proxy Support**: Route requests through HTTP/HTTPS proxies
- **Flexible Configuration**: Customizable thresholds, test values, and detection parameters
- **Real-time Results**: Live output with color-coded confidence levels
- **Comprehensive Reporting**: Detailed summary with statistics

## Installation

### Requirements

- Python 3.6+
- Required packages: `requests`, `urllib3`

### Install Dependencies

```bash
pip install requests urllib3
```

## Usage

### Basic Syntax

```bash
python param_scanner.py -u <URL> -w <WORDLIST> [OPTIONS]
```

### Required Arguments

- `-u, --url`: Target URL (must be valid HTTP/HTTPS URL)
- `-w, --wordlist`: Path to parameter wordlist file (one parameter per line)

### Optional Arguments

- `-m, --method`: HTTP method (GET or POST, default: GET)
- `-d, --data`: POST request body data (supports form-encoded or JSON format)
- `-H, --headers`: Custom headers in format "Header1:value1;Header2:value2"
- `-p, --proxy`: Proxy URL (format: http://host:port or https://host:port)
- `-t, --similarity-threshold`: Response similarity threshold (0.0-1.0, default: 0.95)
- `--min-diff`: Minimum content-length difference to trigger detection (default: 10)
- `--test-value`: Custom test value for parameter injection (default: "fuzzval")
- `--timeout`: Request timeout in seconds (default: 10)
- `--threads`: Number of concurrent threads (default: 1)

## Examples

### Basic GET Parameter Discovery

```bash
python param_scanner.py -u "https://example.com/search" -w common_params.txt
```

### POST Request with Form Data

```bash
python param_scanner.py -u "https://api.example.com/login" -m POST \
  -d "username=admin&password=test" -w api_params.txt
```

### Advanced Scan with Custom Settings

```bash
python param_scanner.py -u "https://target.com/api/data" -w large_wordlist.txt \
  -p http://127.0.0.1:8080 -t 0.90 --min-diff 5 --test-value "FUZZ123" --threads 5
```

### JSON POST Request with Custom Headers

```bash
python param_scanner.py -u "https://api.example.com/endpoint" -m POST \
  -d '{"existing":"data"}' -w params.txt \
  -H "Content-Type:application/json;Authorization:Bearer token123"
```

## Detection Methods

### High Confidence Detection

1. **Reflection Detection**: Test value appears in response body
2. **Content-Length Anomaly**: Significant difference in response size (≥ min-diff threshold)

### Medium Confidence Detection

3. **Response Similarity**: Response differs significantly from baseline (< similarity threshold)
4. **Status Code Change**: Different HTTP status code from baseline

### Low Confidence Detection

5. **Response Time Anomaly**: Significant deviation in response time (>50% difference)
6. **Header Changes**: New or modified response headers

## Output Format

```
[BASELINE] Status: 200, Length: 1234, Time: 0.45s
[+] debug | REFLECTED + LEN_DIFF(+125) | Status: 200, Length: 1359, Time: 0.52s
[?] admin | STATUS_CHANGE | Status: 403, Length: 1234, Time: 0.41s
[~] test | TIME_ANOMALY(1.23s) | Status: 200, Length: 1234, Time: 1.23s
[-] invalid | NO_DETECTION | Status: 200, Length: 1234, Time: 0.43s

=== DISCOVERY SUMMARY ===
High Confidence: 1 parameters
Medium Confidence: 1 parameters
Low Confidence: 1 parameters
Total Requests: 1000
Success Rate: 99.8%
```

### Color Coding

- 🟢 **Green [+]**: High confidence detections
- 🟡 **Yellow [?]**: Medium confidence detections  
- 🔵 **Blue [~]**: Low confidence detections
- ⚪ **White [-]**: No detection

## Wordlist Format

Create wordlist files with one parameter name per line:

```
id
user
admin
debug
test
api_key
token
search
query
```

Comments (lines starting with #) are ignored.

## Testing

A test server is included for testing the tool:

```bash
# Start test server
python test_param_scanner.py

# In another terminal, test the scanner
python param_scanner.py -u http://localhost:8080 -w common_params.txt
```

The test server includes special responses for parameters like `debug`, `admin`, `test`, etc.

## Security Considerations

- **SSL Verification**: Disabled by default for testing (shows warning)
- **Rate Limiting**: Use appropriate thread counts to avoid overwhelming targets
- **Authorization**: Ensure you have permission to test target applications
- **Proxy Usage**: Consider using proxies for anonymity and traffic routing

## Troubleshooting

### Common Issues

1. **Connection Errors**: Check URL format, network connectivity, and firewall settings
2. **SSL Errors**: Tool disables SSL verification by default
3. **Timeout Issues**: Increase timeout value for slow servers
4. **Memory Usage**: Reduce thread count for large wordlists

### Debug Tips

- Start with single-threaded execution (`--threads 1`) for debugging
- Use verbose output to identify issues
- Test with small wordlists first
- Verify baseline establishment before full scan

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the tool.

## License

This tool is provided for educational and authorized testing purposes only. Users are responsible for ensuring they have proper authorization before testing any systems.

## Author

Created by Augment Agent - Version 1.0
