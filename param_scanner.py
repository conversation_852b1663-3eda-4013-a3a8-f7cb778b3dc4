#!/usr/bin/env python3
"""
ParamDiscovery - HTTP Parameter Discovery Tool
Author: Augment Agent
Version: 1.0

A comprehensive Python HTTP parameter discovery tool that identifies hidden or 
undocumented parameters in web applications through systematic fuzzing of GET and POST requests.
"""

import argparse
import requests
import json
import time
import threading
import difflib
import sys
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib3.exceptions import InsecureRequestWarning
from typing import Dict, List, Tuple, Optional, Any

# Disable SSL warnings
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class Colors:
    """ANSI color codes for terminal output"""
    GREEN = '\033[92m'  # High confidence
    YELLOW = '\033[93m'  # Medium confidence
    BLUE = '\033[94m'   # Low confidence
    RED = '\033[91m'    # Errors
    RESET = '\033[0m'   # Reset color
    BOLD = '\033[1m'    # Bold text

class ParameterScanner:
    """Main parameter discovery scanner class"""
    
    def __init__(self, args):
        self.url = args.url
        self.wordlist_path = args.wordlist
        self.method = args.method.upper()
        self.data = args.data
        self.headers = self._parse_headers(args.headers)
        self.proxy = args.proxy
        self.similarity_threshold = args.similarity_threshold
        self.min_diff = args.min_diff
        self.test_value = args.test_value
        self.timeout = args.timeout
        self.threads = args.threads
        
        # Initialize session with default settings
        self.session = requests.Session()
        self.session.verify = False
        
        # Set default User-Agent if not provided
        if 'User-Agent' not in self.headers:
            self.headers['User-Agent'] = 'ParamDiscovery/1.0'
        
        # Configure proxy if provided
        if self.proxy:
            self.session.proxies = {'http': self.proxy, 'https': self.proxy}
        
        # Baseline response storage
        self.baseline = None
        
        # Results tracking
        self.high_confidence = []
        self.medium_confidence = []
        self.low_confidence = []
        self.total_requests = 0
        self.successful_requests = 0
        
        # Thread lock for output synchronization
        self.output_lock = threading.Lock()
    
    def _parse_headers(self, headers_str: Optional[str]) -> Dict[str, str]:
        """Parse headers string into dictionary"""
        headers = {}
        if headers_str:
            for header_pair in headers_str.split(';'):
                if ':' in header_pair:
                    key, value = header_pair.split(':', 1)
                    headers[key.strip()] = value.strip()
        return headers
    
    def _make_request(self, url: str, method: str = 'GET', data: Optional[str] = None, 
                     headers: Optional[Dict[str, str]] = None, retries: int = 3) -> Optional[requests.Response]:
        """Make HTTP request with retry logic"""
        if headers is None:
            headers = self.headers
        
        for attempt in range(retries):
            try:
                start_time = time.time()
                
                if method == 'GET':
                    response = self.session.get(url, headers=headers, timeout=self.timeout)
                else:  # POST
                    # Determine content type and prepare data
                    content_type = headers.get('Content-Type', 'application/x-www-form-urlencoded')
                    
                    if 'application/json' in content_type and data:
                        try:
                            json_data = json.loads(data)
                            response = self.session.post(url, json=json_data, headers=headers, timeout=self.timeout)
                        except json.JSONDecodeError:
                            response = self.session.post(url, data=data, headers=headers, timeout=self.timeout)
                    else:
                        response = self.session.post(url, data=data, headers=headers, timeout=self.timeout)
                
                response.response_time = time.time() - start_time
                self.total_requests += 1
                self.successful_requests += 1
                return response
                
            except (requests.exceptions.RequestException, Exception) as e:
                if attempt == retries - 1:
                    with self.output_lock:
                        print(f"{Colors.RED}[ERROR] Request failed after {retries} attempts: {str(e)}{Colors.RESET}")
                    self.total_requests += 1
                    return None
                time.sleep(0.5 * (attempt + 1))  # Exponential backoff
        
        return None
    
    def _establish_baseline(self) -> bool:
        """Establish baseline response for comparison"""
        response = self._make_request(self.url, self.method, self.data)
        if not response:
            print(f"{Colors.RED}[ERROR] Failed to establish baseline{Colors.RESET}")
            return False

        self.baseline = {
            'status_code': response.status_code,
            'content_length': len(response.content),
            'response_time': response.response_time,
            'headers': dict(response.headers),
            'content': response.text,
            'content_hash': hash(response.text)
        }

        return True

    def _inject_parameter(self, param_name: str) -> Tuple[str, Optional[str], Dict[str, str]]:
        """Inject parameter into URL or data based on method"""
        headers = self.headers.copy()

        if self.method == 'GET':
            # Inject into URL
            parsed_url = urllib.parse.urlparse(self.url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            query_params[param_name] = [self.test_value]

            new_query = urllib.parse.urlencode(query_params, doseq=True)
            new_url = urllib.parse.urlunparse((
                parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                parsed_url.params, new_query, parsed_url.fragment
            ))

            return new_url, self.data, headers

        else:  # POST method
            content_type = headers.get('Content-Type', 'application/x-www-form-urlencoded')

            if 'application/json' in content_type:
                # Inject into JSON data
                try:
                    if self.data:
                        json_data = json.loads(self.data)
                    else:
                        json_data = {}

                    json_data[param_name] = self.test_value
                    new_data = json.dumps(json_data)

                except json.JSONDecodeError:
                    # Fallback to form data if JSON parsing fails
                    if self.data:
                        new_data = f"{self.data}&{param_name}={self.test_value}"
                    else:
                        new_data = f"{param_name}={self.test_value}"
                    headers['Content-Type'] = 'application/x-www-form-urlencoded'

            else:
                # Inject into form data
                if self.data:
                    new_data = f"{self.data}&{param_name}={self.test_value}"
                else:
                    new_data = f"{param_name}={self.test_value}"

            return self.url, new_data, headers

    def _analyze_response(self, param_name: str, response: requests.Response) -> Tuple[str, List[str]]:
        """Analyze response and determine detection confidence"""
        if not response or not self.baseline:
            return "NO_DETECTION", []

        detections = []
        confidence = "NO_DETECTION"

        # Primary Detection (High Confidence)
        # 1. Reflection Detection
        if self.test_value.lower() in response.text.lower():
            detections.append("REFLECTED")
            confidence = "HIGH"

        # 2. Content-Length Anomaly
        length_diff = abs(len(response.content) - self.baseline['content_length'])
        if length_diff >= self.min_diff:
            detections.append(f"LEN_DIFF({'+' if len(response.content) > self.baseline['content_length'] else '-'}{length_diff})")
            if confidence != "HIGH":
                confidence = "HIGH"

        # Secondary Detection (Medium Confidence)
        # 3. Response Similarity
        similarity = difflib.SequenceMatcher(None, self.baseline['content'], response.text).ratio()
        if similarity < self.similarity_threshold:
            detections.append(f"SIMILARITY({similarity:.2f})")
            if confidence == "NO_DETECTION":
                confidence = "MEDIUM"

        # 4. Status Code Change
        if response.status_code != self.baseline['status_code']:
            detections.append("STATUS_CHANGE")
            if confidence == "NO_DETECTION":
                confidence = "MEDIUM"

        # 5. Header Changes
        baseline_headers = set(self.baseline['headers'].keys())
        response_headers = set(response.headers.keys())
        if baseline_headers != response_headers:
            detections.append("HEADER_CHANGE")
            if confidence == "NO_DETECTION":
                confidence = "LOW"

        return confidence, detections

    def _test_parameter(self, param_name: str) -> None:
        """Test a single parameter"""
        try:
            # Inject parameter and make request
            test_url, test_data, test_headers = self._inject_parameter(param_name)
            response = self._make_request(test_url, self.method, test_data, test_headers)

            if response:
                confidence, detections = self._analyze_response(param_name, response)

                # Store results based on confidence
                result = {
                    'param': param_name,
                    'confidence': confidence,
                    'detections': detections,
                    'status_code': response.status_code,
                    'content_length': len(response.content),
                    'response_time': response.response_time
                }

                if confidence == "HIGH":
                    self.high_confidence.append(result)
                elif confidence == "MEDIUM":
                    self.medium_confidence.append(result)
                elif confidence == "LOW":
                    self.low_confidence.append(result)

                # Real-time output - only show detected parameters
                if confidence != "NO_DETECTION":
                    self._print_result(result)

        except Exception as e:
            pass  # Silently handle errors

    def _print_result(self, result: Dict[str, Any]) -> None:
        """Print formatted result with color coding"""
        param = result['param']
        confidence = result['confidence']
        detections = result['detections']

        # Choose color and symbol based on confidence
        if confidence == "HIGH":
            color = Colors.GREEN
            symbol = "[+]"
        elif confidence == "MEDIUM":
            color = Colors.YELLOW
            symbol = "[?]"
        elif confidence == "LOW":
            color = Colors.BLUE
            symbol = "[~]"
        else:
            return  # Don't print if no detection

        detection_str = " + ".join(detections) if detections else ""

        with self.output_lock:
            print(f"{color}{symbol} {param} | {detection_str}{Colors.RESET}")

    def load_wordlist(self) -> List[str]:
        """Load parameter names from wordlist file"""
        try:
            with open(self.wordlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                params = [line.strip() for line in f if line.strip() and not line.startswith('#')]

            return params

        except FileNotFoundError:
            print(f"{Colors.RED}[ERROR] Wordlist file not found: {self.wordlist_path}{Colors.RESET}")
            sys.exit(1)
        except Exception as e:
            print(f"{Colors.RED}[ERROR] Failed to load wordlist: {str(e)}{Colors.RESET}")
            sys.exit(1)

    def scan(self) -> None:
        """Main scanning function"""
        print(f"{Colors.BOLD}ParamDiscovery - Scanning {self.url}{Colors.RESET}")

        # Establish baseline
        if not self._establish_baseline():
            return

        # Load wordlist
        parameters = self.load_wordlist()
        if not parameters:
            print(f"{Colors.RED}[ERROR] No parameters to test{Colors.RESET}")
            return

        print(f"Testing {len(parameters)} parameters...\n")

        # Start scanning
        start_time = time.time()

        if self.threads == 1:
            # Single-threaded execution
            for param in parameters:
                self._test_parameter(param)
        else:
            # Multi-threaded execution
            with ThreadPoolExecutor(max_workers=self.threads) as executor:
                futures = [executor.submit(self._test_parameter, param) for param in parameters]

                # Wait for all tasks to complete
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception:
                        pass  # Silently handle errors

        # Print final summary
        self._print_summary(time.time() - start_time)

    def _print_summary(self, scan_time: float) -> None:
        """Print final summary report"""
        total_found = len(self.high_confidence) + len(self.medium_confidence) + len(self.low_confidence)

        if total_found > 0:
            print(f"\n{Colors.BOLD}Found {total_found} parameters:{Colors.RESET}")
            print(f"{Colors.GREEN}High: {len(self.high_confidence)}{Colors.RESET} | "
                  f"{Colors.YELLOW}Medium: {len(self.medium_confidence)}{Colors.RESET} | "
                  f"{Colors.BLUE}Low: {len(self.low_confidence)}{Colors.RESET}")
        else:
            print(f"\n{Colors.BOLD}No parameters detected{Colors.RESET}")

        print(f"Scan completed in {scan_time:.1f}s")


def create_parser() -> argparse.ArgumentParser:
    """Create and configure argument parser"""
    parser = argparse.ArgumentParser(
        description="ParamDiscovery - HTTP Parameter Discovery Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic GET parameter discovery
  python param_scanner.py -u "https://example.com/search" -w common_params.txt

  # POST request with form data and custom headers
  python param_scanner.py -u "https://api.example.com/login" -m POST \\
    -d "username=admin&password=test" -w api_params.txt \\
    -H "Authorization:Bearer token123;X-API-Key:secret"

  # Advanced scan with proxy and custom thresholds
  python param_scanner.py -u "https://target.com/api/data" -w large_wordlist.txt \\
    -p http://127.0.0.1:8080 -t 0.90 --min-diff 5 --test-value "FUZZ123" --threads 5

Author: Augment Agent
Version: 1.0
        """
    )

    # Required arguments
    parser.add_argument('-u', '--url', required=True,
                       help='Target URL (must be valid HTTP/HTTPS URL)')
    parser.add_argument('-w', '--wordlist', required=True,
                       help='Path to parameter wordlist file (one parameter per line)')

    # Optional arguments
    parser.add_argument('-m', '--method', choices=['GET', 'POST'], default='GET',
                       help='HTTP method (default: GET)')
    parser.add_argument('-d', '--data',
                       help='POST request body data (supports form-encoded or JSON format)')
    parser.add_argument('-H', '--headers',
                       help='Custom headers in format "Header1:value1;Header2:value2"')
    parser.add_argument('-p', '--proxy',
                       help='Proxy URL (format: http://host:port or https://host:port)')
    parser.add_argument('-t', '--similarity-threshold', type=float, default=0.95,
                       help='Response similarity threshold (float 0.0-1.0, default: 0.95)')
    parser.add_argument('--min-diff', type=int, default=10,
                       help='Minimum content-length difference to trigger detection (default: 10)')
    parser.add_argument('--test-value', default='fuzzval',
                       help='Custom test value for parameter injection (default: "fuzzval")')
    parser.add_argument('--timeout', type=int, default=10,
                       help='Request timeout in seconds (default: 10)')
    parser.add_argument('--threads', type=int, default=1,
                       help='Number of concurrent threads (default: 1)')

    return parser


def validate_args(args) -> bool:
    """Validate command line arguments"""
    # Validate URL
    if not args.url.startswith(('http://', 'https://')):
        print(f"{Colors.RED}[ERROR] URL must start with http:// or https://{Colors.RESET}")
        return False

    # Validate similarity threshold
    if not 0.0 <= args.similarity_threshold <= 1.0:
        print(f"{Colors.RED}[ERROR] Similarity threshold must be between 0.0 and 1.0{Colors.RESET}")
        return False

    # Validate threads
    if args.threads < 1:
        print(f"{Colors.RED}[ERROR] Thread count must be at least 1{Colors.RESET}")
        return False

    # Validate timeout
    if args.timeout < 1:
        print(f"{Colors.RED}[ERROR] Timeout must be at least 1 second{Colors.RESET}")
        return False

    # Validate min_diff
    if args.min_diff < 0:
        print(f"{Colors.RED}[ERROR] Minimum difference must be non-negative{Colors.RESET}")
        return False

    return True


def main():
    """Main entry point"""
    parser = create_parser()
    args = parser.parse_args()

    # Validate arguments
    if not validate_args(args):
        sys.exit(1)

    try:
        # Create and run scanner
        scanner = ParameterScanner(args)
        scanner.scan()

    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}[INFO] Scan interrupted by user{Colors.RESET}")
        sys.exit(0)
    except Exception as e:
        print(f"{Colors.RED}[ERROR] Unexpected error: {str(e)}{Colors.RESET}")
        sys.exit(1)


if __name__ == "__main__":
    main()
